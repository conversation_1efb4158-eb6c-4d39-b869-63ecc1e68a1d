import { Field, InputType, ObjectType, Int } from '@nestjs/graphql'
import { IsString, IsArray, IsNumber, IsOptional } from 'class-validator'

@InputType()
export class BulkStartCertificationInput {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    participantIds: string[]

    @Field(() => Int)
    @IsNumber()
    year: number
}

@InputType()
export class BulkApproveCertificationInput {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    certificationIds: string[]
}

@InputType()
export class BulkRejectCertificationInput {
    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    certificationIds: string[]

    @Field()
    @IsString()
    reason: string
}

@InputType()
export class GetCertificationStatsInput {
    @Field(() => Int)
    @IsNumber()
    year: number
}

@InputType()
export class GetAutoApproveEligibleInput {
    @Field(() => Int)
    @IsNumber()
    year: number
}

@InputType()
export class GetCommonChangePatternsInput {
    @Field(() => Int)
    @IsNumber()
    year: number
}

@ObjectType()
export class BulkOperationFailure {
    @Field()
    participantId?: string

    @Field()
    certificationId?: string

    @Field()
    reason: string
}

@ObjectType()
export class BulkStartCertificationResponse {
    @Field(() => [String])
    successful: string[]

    @Field(() => [BulkOperationFailure])
    failed: BulkOperationFailure[]
}

@ObjectType()
export class BulkApproveCertificationResponse {
    @Field(() => [String])
    successful: string[]

    @Field(() => [BulkOperationFailure])
    failed: BulkOperationFailure[]
}

@ObjectType()
export class BulkRejectCertificationResponse {
    @Field(() => [String])
    successful: string[]

    @Field(() => [BulkOperationFailure])
    failed: BulkOperationFailure[]
}

@ObjectType()
export class CertificationStats {
    @Field(() => Int)
    totalParticipants: number

    @Field(() => Int)
    pendingCertifications: number

    @Field(() => Int)
    inProgress: number

    @Field(() => Int)
    completed: number

    @Field(() => Int)
    requiresAttention: number
}

@ObjectType()
export class AutoApproveEligible {
    @Field()
    certificationId: string

    @Field()
    participantId: string

    @Field()
    participantName: string

    @Field(() => Int)
    changes: number
}

@ObjectType()
export class CommonChangePattern {
    @Field()
    field: string

    @Field(() => Int)
    count: number

    @Field(() => [String])
    certificationIds: string[]
}
