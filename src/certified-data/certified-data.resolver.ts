import { Resolver, Query, Mutation, Args, Context, Int } from '@nestjs/graphql'
import {
    UnauthorizedException,
    UseGuards,
    BadRequestException,
} from '@nestjs/common'
import { CertifiedDataService } from './certified-data.service'
import { CertifiedData, BulkCertificationDataResponse } from './entities'
import { CreateCertifiedDataInput } from './dto'
import { UpdateCertifiedDataInput } from './dto'
import { FindAllCertifiedDataInput } from './dto'
import { FindOneCertifiedDataInput } from './dto'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { PaginatedCertifiedData } from './entities'
import { ParticipantService } from '../participant/participant.service'
import { UserService } from '../user/user.service'
import { CertifiedDataByYearResponse } from './entities/certified-by-year.entity'
import {
    RevertChangesResponse,
    RevertChangesPreview,
} from './dto/revert-certification-changes.input'
import {
    RevertCertificationChangesInput,
    RevertCertificationChangesWithConfirmationInput,
    PreviewRevertChangesInput,
} from './dto/revert-certification-changes-input.dto'
import {
    UpdateCertifiedDataApprovedChangesInput,
    UpdateCertifiedDataRejectedChangesInput,
    UpdateCertificationStatusInput,
} from './dto/update-certified-data-changes.input'
import {
    RevertSingleFieldInput,
    RevertSingleFieldResponse,
} from './dto/revert-single-field.input'
import {
    BulkStartCertificationInput,
    BulkApproveCertificationInput,
    BulkRejectCertificationInput,
    GetCertificationStatsInput,
    GetAutoApproveEligibleInput,
    GetCommonChangePatternsInput,
    BulkStartCertificationResponse,
    BulkApproveCertificationResponse,
    BulkRejectCertificationResponse,
    CertificationStats,
    AutoApproveEligible,
    CommonChangePattern,
} from './dto'
import { GetCertificationParticipantsInput } from './dto/get-certification-participants.input'
import { CertificationParticipant } from './entities/certification-participant.entity'

@Resolver(() => CertifiedData)
export class CertifiedDataResolver {
    constructor(private readonly certifiedDataService: CertifiedDataService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedData)
    async createCertifiedData(
        @Args('createCertifiedDataInput')
        createCertifiedDataInput: CreateCertifiedDataInput,
        @Args('participantId', { type: () => String }) participantId: string,
        @Args('certifiedById', { type: () => String }) certifiedById: string,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.certifiedDataService.create(
            createCertifiedDataInput,
            participantId,
            certifiedById,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => PaginatedCertifiedData, { name: 'getAllCertifiedData' })
    async findAll(
        @Args('findAllCertifiedDataInput', { nullable: true })
        findAllCertifiedDataInput: FindAllCertifiedDataInput = {}
    ) {
        return this.certifiedDataService.findAll(findAllCertifiedDataInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => BulkCertificationDataResponse, { name: 'getBulkCertificationData' })
    async getBulkCertificationData(
        @Args('participantIds', { type: () => [String] }) participantIds: string[],
        @Args('year', { type: () => Int }) year: number
    ): Promise<BulkCertificationDataResponse> {
        return this.certifiedDataService.getBulkCertificationData(participantIds, year)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedData, { name: 'getCertifiedDataById' })
    async findOne(
        @Args('findOneCertifiedDataInput')
        findOneCertifiedDataInput: FindOneCertifiedDataInput
    ) {
        return this.certifiedDataService.findOne(findOneCertifiedDataInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [CertifiedData], { name: 'getParticipantCertifiedData' })
    async findByParticipant(
        @Args('participantId', { type: () => String }) participantId: string
    ) {
        return this.certifiedDataService.findByParticipant(participantId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [CertifiedData], { name: 'yearCertifications' })
    async findByYear(
        @Args('certificationYear', { type: () => Int })
        certificationYear: number
    ) {
        return this.certifiedDataService.findByYear(certificationYear)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedData, {
        name: 'latestParticipantCertification',
        nullable: true,
    })
    async findLatestForParticipant(
        @Args('participantId', { type: () => String }) participantId: string
    ) {
        return this.certifiedDataService.findLatestForParticipant(participantId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedData)
    async updateCertificationStatus(
        @Args('input') input: UpdateCertificationStatusInput,
        @Context() context: any
    ) {
        const userId = context.req.user.id
        return this.certifiedDataService.updateStatus(
            input.id,
            input.status,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertifiedDataByYearResponse, {
        name: 'getCertifiedDataByYearAndYearBefore',
    })
    async findCertifiedDataByYearAndYearBefore(
        @Args('certificationYear', { type: () => Int })
        certificationYear: number
    ) {
        return this.certifiedDataService.findCertifiedDataByYearAndYearBefore(
            certificationYear
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedData)
    async updateCertifiedDataApprovedChanges(
        @Args('id', { type: () => String }) id: string,
        @Args('approvedChanges', { type: () => [String] })
        approvedChanges: string[],
        @Args('entityType', { type: () => String, nullable: true })
        entityType?: string,
        @Args('entityId', { type: () => String, nullable: true })
        entityId?: string
    ) {
        return this.certifiedDataService.updateApprovedChanges(
            id,
            approvedChanges,
            entityType,
            entityId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => RevertChangesResponse)
    async revertApprovedRejectedChanges(
        @Args('certificationYear', { type: () => Int })
        certificationYear: number,
        @Context() context: any
    ): Promise<RevertChangesResponse> {
        const userId =
            context?.req?.user?.pensionUserId || context?.user?.pensionUserId

        return await this.certifiedDataService.revertApprovedRejectedChanges(
            certificationYear,
            userId
        )
    }

    // Query to preview what would be reverted (dry-run)
    @UseGuards(GraphqlAuthGuard)
    @Query(() => RevertChangesPreview)
    async previewRevertChanges(
        @Args('certificationYear', { type: () => Int })
        certificationYear: number
    ): Promise<RevertChangesPreview> {
        return await this.certifiedDataService.previewRevertChanges(
            certificationYear
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CertifiedData)
    async updateCertifiedDataRejectedChanges(
        @Args('id', { type: () => String }) id: string,
        @Args('rejectedChanges', { type: () => [String] })
        rejectedChanges: string[],
        @Context() context: any,
        @Args('entityType', { type: () => String, nullable: true })
        entityType?: string,
        @Args('rejectReason', { type: () => String, nullable: true })
        rejectReason?: string,
        @Args('entityId', { type: () => String, nullable: true })
        entityId?: string
    ) {
        // const userId = context.req.user.id
        const userId = 'be196c0f-ee7b-42a3-9163-885f649e65ef' //TODO: Remove this after user claims are fixed
        return this.certifiedDataService.updateRejectedChanges(
            id,
            rejectedChanges,
            entityType,
            rejectReason || '',
            userId,
            entityId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => RevertSingleFieldResponse)
    async revertSingleField(
        @Args('input') input: RevertSingleFieldInput,
        @Context() context: any
    ): Promise<RevertSingleFieldResponse> {
        const dummyUserId = 'be196c0f-ee7b-42a3-9163-885f649e65ef' //TODO: Remove this after user claims are fixed

        const userId = context?.req?.user?.pensionUserId || dummyUserId

        return await this.certifiedDataService.revertSingleField(input, userId)
    }

    // New bulk certification resolvers
    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => BulkStartCertificationResponse)
    async bulkStartCertification(
        @Args('input') input: BulkStartCertificationInput,
        @Context() context: any
    ): Promise<BulkStartCertificationResponse> {
        const dummyUserId = 'be196c0f-ee7b-42a3-9163-885f649e65ef' //TODO: Remove this after user claims are fixed
        const userId = context?.req?.user?.pensionUserId || dummyUserId

        return await this.certifiedDataService.bulkStartCertification(
            input.participantIds,
            input.year,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => BulkApproveCertificationResponse)
    async bulkApproveCertification(
        @Args('input') input: BulkApproveCertificationInput,
        @Context() context: any
    ): Promise<BulkApproveCertificationResponse> {
        const dummyUserId = 'be196c0f-ee7b-42a3-9163-885f649e65ef' //TODO: Remove this after user claims are fixed
        const userId = context?.req?.user?.pensionUserId || dummyUserId

        return await this.certifiedDataService.bulkApproveCertification(
            input.certificationIds,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => BulkRejectCertificationResponse)
    async bulkRejectCertification(
        @Args('input') input: BulkRejectCertificationInput,
        @Context() context: any
    ): Promise<BulkRejectCertificationResponse> {
        const dummyUserId = 'be196c0f-ee7b-42a3-9163-885f649e65ef' //TODO: Remove this after user claims are fixed
        const userId = context?.req?.user?.pensionUserId || dummyUserId

        return await this.certifiedDataService.bulkRejectCertification(
            input.certificationIds,
            input.reason,
            userId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => CertificationStats)
    async getCertificationStats(
        @Args('input') input: GetCertificationStatsInput
    ): Promise<CertificationStats> {
        return await this.certifiedDataService.getCertificationStats(input.year)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [AutoApproveEligible])
    async getAutoApproveEligible(
        @Args('input') input: GetAutoApproveEligibleInput
    ): Promise<AutoApproveEligible[]> {
        return await this.certifiedDataService.getAutoApproveEligible(
            input.year
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [CommonChangePattern])
    async getCommonChangePatterns(
        @Args('input') input: GetCommonChangePatternsInput
    ): Promise<CommonChangePattern[]> {
        return await this.certifiedDataService.getCommonChangePatterns(
            input.year
        )
    }

    // New query to fetch certification participants by year with conditional source
    @UseGuards(GraphqlAuthGuard)
    @Query(() => [CertificationParticipant])
    async getCertificationParticipants(
        @Args('input') input: GetCertificationParticipantsInput
    ): Promise<CertificationParticipant[]> {
        return await this.certifiedDataService.getCertificationParticipants(
            input
        )
    }
}
