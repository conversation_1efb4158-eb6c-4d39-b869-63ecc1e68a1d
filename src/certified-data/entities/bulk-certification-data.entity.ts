import { ObjectType, Field, Int } from '@nestjs/graphql'
import { CertifiedData } from './certified-data.entity'

@ObjectType()
export class BulkCertificationStats {
    @Field(() => Int)
    totalParticipants: number

    @Field(() => Int)
    totalChanges: number

    @Field(() => Int)
    noChanges: number

    @Field(() => Int)
    highRisk: number
}

@ObjectType()
export class BulkChangePattern {
    @Field()
    field: string

    @Field(() => Int)
    count: number

    @Field(() => [String])
    participants: string[]
}

@ObjectType()
export class BulkCertificationDataResponse {
    @Field(() => [CertifiedData])
    certifications: CertifiedData[]

    @Field(() => BulkCertificationStats)
    stats: BulkCertificationStats

    @Field(() => [BulkChangePattern])
    changePatterns: BulkChangePattern[]
}
